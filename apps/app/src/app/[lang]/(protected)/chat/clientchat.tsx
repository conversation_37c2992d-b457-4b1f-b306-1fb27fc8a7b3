"use client"
import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import Image from "next/image"

import { ChatInput } from "@/components/chatcomponents/chatinput"
import { ChatMessage } from "@/components/chatcomponents/chatmessage"
import { updateFolderModifiedStatus } from "@/components/chatcomponents/sidebar"
import { ProjectInfoComponent } from "@/components/project/project-info"
import { chunkAndEmbedDocument } from "@/lib/folder-utils"
import { getAllFolders } from "@/lib/folder-utils"
import { trpcClient } from "@/lib/trpc/client"
import type { FolderType } from "@/types/index-type"
import { type Message, useChat } from "@ai-sdk/react"
import { logger } from "@buildismart/lib"
type GenerateSummaryResponse = {
  summary: string
}

interface EnhancedMessage extends Message {
  fileData?: {
    name: string
    type?: string
    url?: string
    summary?: string
    shortSummary?: string
    isDirectory?: boolean
    nombrefichiers?: number
    texteextrait?: string
  }
}
interface MessageData {
  isUpdate?: boolean
  file?: {
    id: string
    name: string
    type?: string
    summary?: string
    shortSummary?: string
    texteextrait?: string
    isDirectory?: boolean
    nombrefichiers?: number
  }
}

interface ClientChatProps {
  initialFolder?: {
    id: string
    title: string
    projectId: string
    Ismodified: boolean
    texteextraitdossier: string
    nombrefichiers: number
    files: Array<{
      id?: string
      name: string
      type: string
      url?: string
      summary?: string
      textextraitfichier?: string
    }>
  } | null
}
const generateProjectSummary = async (projectId: string, projectSummary: string) => {
  try {
    const response = await fetch("/api/generate-project-summary", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ projectSummary }),
    })

    if (!response.ok) {
      throw new Error("Erreur lors de la génération du résumé")
    }

    const data = (await response.json()) as GenerateSummaryResponse
    return data.summary
  } catch (error) {
    logger.error("Erreur:", error)
    return projectSummary // Fallback au summary original en cas d'erreur
  }
}
// Removed old DocumentInfoSection - replaced with ProjectInfoComponent
export default function ClientChat({ initialFolder }: ClientChatProps) {
  // Pour le chat général (sans projet), utiliser un ID par défaut
  const sessionId = initialFolder?.id || "general-chat"

  // Configuration différente selon si on a un projet ou non
  const chatConfig = initialFolder?.id
    ? {
      api: "/api/chat",
      id: sessionId,
      body: {
        projectId: initialFolder.id,
      },
    }
    : {
      api: "/api/chat",
      id: sessionId,
      // Pas de projectId pour le chat général
    }

  const { messages, input, handleInputChange, append, isLoading } = useChat(chatConfig)

  const [extractedContent, _i] = useState<string | null>(null)
  const [hasSentInitialData, setHasSentInitialData] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [_, setFolders] = useState<FolderType[]>([])
  const [shortSummary, setShortSummary] = useState<string | null>(null)
  const [projectStatus, setProjectStatus] = useState<{
    status: string
    message: string
    isComplete: boolean
    totalDocuments: number
    readyDocuments: number
    errorDocuments: number
  } | null>(null)
  const [isChatEnabled, setIsChatEnabled] = useState(false)
  const i = useRef(true)
  const handleProcess = useCallback(async () => {
    setIsProcessing(true)

    try {
      if (initialFolder) {
        await handleProcessFolder(initialFolder)
      }
    } finally {
      setIsProcessing(false)
    }
  }, [initialFolder])
  useEffect(() => {
    const initialDataSentKey = initialFolder?.id
    const hasAlreadySent = initialDataSentKey ? localStorage.getItem(`sent_${initialDataSentKey}`) : null

    if (initialFolder && !hasSentInitialData && !hasAlreadySent) {
      const processData = async () => {
        await handleProcess()
        setHasSentInitialData(true)
        if (initialDataSentKey) {
          localStorage.setItem(`sent_${initialDataSentKey}`, "true")
        }
        localStorage.setItem("hasVisitedOnce", "true")
        const folders = await getAllFolders()
        await updateFolderModifiedStatus(initialFolder?.id, true, folders, setFolders)
      }
      processData()
    }
  }, [initialFolder, handleProcess, hasSentInitialData])

  // Monitor project status to enable/disable chat
  useEffect(() => {
    if (!initialFolder?.id) return

    const checkProjectStatus = async () => {
      try {
        const status = await trpcClient.folders.getProjectStatus.query({
          projectId: initialFolder.id
        })
        setProjectStatus(status)

        // Enable chat only if there are documents and at least some are ready
        const canChat = status.totalDocuments > 0 && status.readyDocuments > 0
        setIsChatEnabled(canChat)

        logger.log("Project status:", status)
        logger.log("Chat enabled:", canChat)
      } catch (error) {
        logger.error("Error checking project status:", error)
        setIsChatEnabled(false)
      }
    }

    // Check status immediately
    checkProjectStatus()

    // Poll for status updates every 10 seconds if processing
    const interval = setInterval(() => {
      if (projectStatus?.status === "PROCESSING" || !projectStatus) {
        checkProjectStatus()
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [initialFolder?.id, projectStatus?.status])

  const [isPageLoading, setIsPageLoading] = useState(true)
  useEffect(() => {
    // Simule un délai de chargement ou attends que tout soit prêt
    const timeout = setTimeout(() => {
      setIsPageLoading(false)
    }, 1500) // 1.5s ou ajuste selon ton besoin

    return () => clearTimeout(timeout)
  }, [])

  const handleProcessFolder = async (folder: {
    id: string
    title: string
    nombrefichiers: number
    summary?: string
    projectId?: string
    texteextraitdossier: string
    files: Array<{
      id?: string
      name: string
      type: string
      url?: string
      summary?: string
      textextraitfichier?: string
    }>
  }) => {
    logger.log("⚠️ handleProcessFolder appelé !")
    setIsProcessing(true)
    try {
      // Traitement des fichiers
      await Promise.all(folder.files.map((file) => chunkAndEmbedDocument(file.id!)))

      // Mise à jour du résumé
      const generatedShortSummary = await generateProjectSummary(folder.id, folder.texteextraitdossier)
      setShortSummary(generatedShortSummary)
      // Ajout de la réponse assistant dans le chat
      logger.log("i.current")
      logger.log(i.current)

      i.current = false
    } catch (error) {
      logger.error("Erreur lors du traitement du dossier:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSubmit = async (userInput: string) => {
    // Pour le chat général, toujours permettre l'envoi
    // Pour le chat avec projet, vérifier si le chat est activé
    if (initialFolder && !isChatEnabled) {
      logger.warn("Chat is disabled - documents not ready")
      return
    }

    if (userInput.trim()) {
      // Sauvegarder le contenu avant de vider l'input
      const contentToSend = userInput
      const hiddenContent = extractedContent || ""

      // Vider l'input immédiatement
      handleInputChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)

      // Envoyer le message ensuite
      await append({
        content: contentToSend,
        role: "user",
        data: initialFolder ? {
          hiddenContent: hiddenContent,
        } : undefined,
      })
    }
  }
  const filteredMessages = messages.filter(
    (msg) => (msg.role === "user" || msg.role === "assistant") && "content" in msg
  )

  const enhancedMessages: EnhancedMessage[] = filteredMessages.map((msg, index) => {
    const data = msg.data as MessageData

    if ((index > 0 && msg.role === "user") || data?.isUpdate) {
      return msg
    }

    const fileInfo = data?.file

    if (fileInfo?.id) {
      return {
        ...msg,
        fileData: {
          name: fileInfo.name,
          type: fileInfo.type,
          url: fileInfo.id.startsWith("http") ? fileInfo.id : `/api/files/${fileInfo.id}`,
          summary: fileInfo.summary,
          shortSummary: fileInfo.shortSummary,
          texteextrait: fileInfo.texteextrait,
          isDirectory: fileInfo.isDirectory,
          nombrefichiers: fileInfo.nombrefichiers,
        },
      }
    }
    return msg
  })

  const isChatEmpty = enhancedMessages.length === 0

  if (isPageLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white">
        <span className="animate-spin text-2xl text-gray-600">⏳</span>
      </div>
    )
  }
  return (
    <div className="flex h-screen items-center justify-center bg-gray-100 px-2">
      <div className="flex size-full max-w-2xl flex-col">
        <div className="flex-1 overflow-y-auto px-4 py-2 pb-24 scrollbar-hide">
          {/* Afficher les informations du projet en haut, même sans messages */}
          {!isProcessing && initialFolder && (
            <ProjectInfoComponent projectId={initialFolder.id} />
          )}

          {isChatEmpty ? (
            <div className="flex h-full flex-1 flex-col items-center justify-center gap-4 text-center">
              {/* Chat général (sans projet) */}
              {!initialFolder && (
                <div className="space-y-2">
                  <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                  <h2 className="text-xl font-bold text-black">
                    Bienvenue sur BuildiSmart AI !
                  </h2>
                  <p className="text-sm text-gray-500">
                    Votre assistant IA spécialisé dans la construction et le BTP.
                  </p>
                  <p className="text-xs text-gray-400">
                    Posez vos questions générales ou créez un projet pour analyser vos documents.
                  </p>
                </div>
              )}

              {/* Chat avec projet */}
              {initialFolder && isProcessing && ( // Ajout de cette condition pour afficher seulement pendant le traitement
                <>
                  <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                  <div className="space-y-[0.5px]">
                    <h2 className="text-xl font-bold text-black">
                      Bienvenue sur votre assistant d&rsquo;analyse de DCE !
                    </h2>
                    <p className="text-sm text-gray-500">
                      <span className="text-blue-500">Analyse en cours...</span>
                    </p>
                  </div>
                </>
              )}
              {initialFolder && !isProcessing && ( // Message affiché quand le traitement est terminé et le chat est vide
                <div className="space-y-2">
                  <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
                  <h2 className="text-xl font-bold text-black">
                    Bienvenue sur votre assistant d&rsquo;analyse de DCE !
                  </h2>
                  {isChatEnabled ? (
                    <p className="text-sm text-gray-500">
                      Vous pouvez maintenant poser vos questions sur les documents analysés.
                    </p>
                  ) : (
                    <div className="space-y-1">
                      <p className="text-sm text-yellow-600">
                        {projectStatus?.message || "Vérification du statut..."}
                      </p>
                      {projectStatus?.totalDocuments === 0 ? (
                        <p className="text-xs text-gray-500">
                          Veuillez télécharger des documents pour commencer l'analyse.
                        </p>
                      ) : projectStatus?.readyDocuments === 0 ? (
                        <p className="text-xs text-gray-500">
                          Les documents sont en cours de traitement. Le chat sera activé une fois l'analyse terminée.
                        </p>
                      ) : (
                        <p className="text-xs text-gray-500">
                          Préparation du chat en cours...
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4 pb-4">
              {enhancedMessages.map((msg, index) => (
                <div key={index} className="flex w-full max-w-3xl flex-col gap-2">
                  <ChatMessage role={msg.role as "user" | "assistant"} content={msg.content as string} />
                </div>
              ))}
              {isLoading && <ChatMessage role="assistant" content="⏳ Rédaction de la réponse..." />}
            </div>
          )}
        </div>

        <div className="sticky bottom-0 flex justify-center border-t bg-gray-100 py-2">
          <ChatInput
            input={input}
            onInputChange={handleInputChange}
            onSubmit={() => handleSubmit(input)}
            disabled={!isChatEnabled}
            placeholder={
              !isChatEnabled
                ? projectStatus?.totalDocuments === 0
                  ? "Téléchargez des documents pour commencer..."
                  : "Traitement en cours..."
                : "Posez votre question..."
            }
          />
        </div>
      </div>
    </div>
  )
}
