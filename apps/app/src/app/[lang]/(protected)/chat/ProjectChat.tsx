"use client"

import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import Image from "next/image"

import { ChatInput } from "@/components/chatcomponents/chatinput"
import { ChatMessage } from "@/components/chatcomponents/chatmessage"
import { ProjectInfoComponent } from "@/components/project/project-info"
import { trpcClient } from "@/lib/trpc/client"
import type { FolderType } from "@/types/index-type"
import { type Message, useChat } from "@ai-sdk/react"
import { logger } from "@buildismart/lib"

interface ProjectChatProps {
  project: FolderType
}

export default function ProjectChat({ project }: ProjectChatProps) {
  const [chat, setChat] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChatEnabled, setIsChatEnabled] = useState(false)
  const [projectStatus, setProjectStatus] = useState<any>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Utiliser useChat avec un ID personnalisé basé sur le projet
  const {
    messages,
    input,
    handleInputChange,
    append,
    isLoading: isAILoading,
    setMessages,
  } = useChat({
    api: "/api/chat",
    id: `project-${project.id}`,
    body: {
      projectId: project.id,
    },
    onFinish: async (message) => {
      // Sauvegarder le message de l'assistant
      if (chat?.id) {
        await saveMessage(chat.id, "assistant", message.content)
      }
    },
  })

  // Charger ou créer le chat pour ce projet
  useEffect(() => {
    loadChat()
  }, [project.id])

  // Vérifier le statut du projet
  useEffect(() => {
    checkProjectStatus()
  }, [project.id])

  // Auto-scroll vers le bas
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadChat = async () => {
    try {
      setIsLoading(true)
      const chatData = await trpcClient.chat.getOrCreateChat.query({
        projectId: project.id,
      })
      
      setChat(chatData)
      
      // Charger les messages existants dans useChat
      if (chatData.messages.length > 0) {
        const formattedMessages = chatData.messages.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt,
        }))
        setMessages(formattedMessages)
      }
      
      logger.log("Chat loaded:", chatData)
    } catch (error) {
      logger.error("Error loading chat:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkProjectStatus = async () => {
    try {
      const status = await trpcClient.folders.getProjectStatus.query({
        projectId: project.id,
      })
      setProjectStatus(status)

      // Activer le chat seulement si il y a des documents prêts
      const canChat = status.totalDocuments > 0 && status.readyDocuments > 0
      setIsChatEnabled(canChat)

      logger.log("Project status:", status)
      logger.log("Chat enabled:", canChat)
    } catch (error) {
      logger.error("Error checking project status:", error)
      setIsChatEnabled(false)
    }
  }

  const saveMessage = async (chatId: string, role: "user" | "assistant", content: string) => {
    try {
      await trpcClient.chat.saveMessage.mutate({
        chatId,
        role,
        content,
      })
      logger.log("Message saved:", { role, contentLength: content.length })
    } catch (error) {
      logger.error("Error saving message:", error)
    }
  }

  const handleSubmit = async (userInput: string) => {
    if (!isChatEnabled) {
      logger.warn("Chat is disabled - documents not ready")
      return
    }

    if (!chat?.id) {
      logger.warn("No chat available")
      return
    }

    if (userInput.trim()) {
      // Sauvegarder le message de l'utilisateur
      await saveMessage(chat.id, "user", userInput)

      // Vider l'input immédiatement
      handleInputChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)

      // Envoyer le message à l'IA
      await append({
        content: userInput,
        role: "user",
      })
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p>Chargement du chat...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full flex-col">
      {/* En-tête du projet */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center gap-3">
          <Image src="/folder-icon.png" alt="Project" width={24} height={24} className="h-6 w-6" />
          <div>
            <h1 className="font-semibold text-gray-900">{project.title}</h1>
            <p className="text-sm text-gray-500">
              {projectStatus ? `${projectStatus.readyDocuments}/${projectStatus.totalDocuments} documents prêts` : "Chargement..."}
            </p>
          </div>
        </div>
      </div>

      {/* Informations du projet */}
      <div className="border-b bg-gray-50 p-4">
        <ProjectInfoComponent projectId={project.id} />
      </div>

      {/* Zone de messages */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="mx-auto max-w-4xl space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500">
              <p>Aucun message pour le moment.</p>
              {isChatEnabled ? (
                <p className="mt-2">Commencez une conversation en posant une question sur votre projet.</p>
              ) : (
                <p className="mt-2">En attente que les documents soient traités...</p>
              )}
            </div>
          ) : (
            messages.map((message) => (
              <ChatMessage key={message.id} role={message.role as "user" | "assistant"} content={message.content} />
            ))
          )}
          {isAILoading && (
            <div className="flex justify-start">
              <div className="max-w-lg rounded-xl bg-white p-3 shadow-md">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400"></div>
                  <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400" style={{ animationDelay: "0.1s" }}></div>
                  <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400" style={{ animationDelay: "0.2s" }}></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Zone de saisie */}
      <div className="border-t bg-white p-4">
        <div className="mx-auto max-w-4xl">
          <ChatInput
            value={input}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
            disabled={!isChatEnabled || isAILoading}
            placeholder={
              isChatEnabled
                ? "Posez une question sur votre projet..."
                : "En attente du traitement des documents..."
            }
          />
          {!isChatEnabled && projectStatus && (
            <p className="mt-2 text-sm text-gray-500">
              Traitement en cours: {projectStatus.processingDocuments} documents en cours, {projectStatus.readyDocuments} prêts
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
