import React from "react"

import { Sidebar } from "@/components/chatcomponents/sidebar"
import { Topbar } from "@/components/chatcomponents/topbar"

import ClientChatWrapper from "./ClientChatWrapper"

interface ChatPageProps {
  searchParams: { projectId?: string }
}

export default function ChatPage({ searchParams }: ChatPageProps) {
  const { projectId } = searchParams

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - cachée sur mobile par défaut, visible via overlay */}
      <Sidebar folders={[]} />

      {/* Zone principale qui prend toute la largeur sur mobile */}
      <main className="flex w-full flex-1 flex-col overflow-hidden bg-[#F1F2F3] md:w-auto">
        {/* TOPBAR FIXE avec padding pour le bouton mobile */}
        <div className="sticky top-0 z-40 pl-16 md:pl-0">
          <Topbar />
        </div>

        {/* CLIENT CHAT WRAPPER */}
        <ClientChatWrapper projectId={projectId} />
      </main>
    </div>
  )
}
