"use client"

import React, { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"

import { trpcClient } from "@/lib/trpc/client"
import { logger } from "@buildismart/lib"
import { Spinner } from "@nextui-org/react"

import ClientC<PERSON> from "./clientchat"
import ProjectChat from "./ProjectChat"

interface ClientChatWrapperProps {
  projectId?: string
}

export default function ClientChatWrapper({ projectId }: ClientChatWrapperProps) {
  const [project, setProject] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    if (projectId) {
      loadProject()
    }
  }, [projectId])

  const loadProject = async () => {
    if (!projectId) return

    setLoading(true)
    setError(null)

    try {
      // Utiliser le tRPC pour récupérer les informations du projet
      const projectData = await trpcClient.folders.getById.query({ id: projectId })
      setProject(projectData)
      logger.log("Project loaded for chat:", projectData)
    } catch (error) {
      logger.error("Error loading project:", error)
      setError("Erreur lors du chargement du projet")
      // Rediriger vers le chat général en cas d'erreur
      router.push("/chat")
    } finally {
      setLoading(false)
    }
  }

  // Chat général (sans projet)
  if (!projectId) {
    return <ClientChat />
  }

  // État de chargement
  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Spinner size="lg" label="Chargement du projet..." />
      </div>
    )
  }

  // État d'erreur
  if (error) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-4">
        <h2 className="text-xl font-semibold text-red-600">Erreur</h2>
        <p className="text-gray-600">{error}</p>
        <button
          onClick={() => router.push("/chat")}
          className="rounded bg-primary px-4 py-2 text-white hover:bg-primary/90"
        >
          Retour au chat général
        </button>
      </div>
    )
  }

  // Chat spécifique au projet
  if (project) {
    return <ProjectChat project={project} />
  }

  // Fallback
  return <ClientChat />
}
