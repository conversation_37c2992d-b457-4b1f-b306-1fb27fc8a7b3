// apps/app/src/components/chatcomponents/chatinput.tsx
"use client"
import React from "react"
import { ArrowUp } from "lucide-react"

function Spinner() {
  return <div className="size-5 animate-spin rounded-full border-b-2 border-white"></div>
}

interface ChatInputProps {
  input: string
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: () => void
  disabled?: boolean
  placeholder?: string
}

export function ChatInput({
  input,
  onInputChange,
  onSubmit,
  disabled,
  placeholder = "Poser une question...",
}: ChatInputProps) {
  const [isSending, setIsSending] = React.useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSending(true)
    await onSubmit()
    setIsSending(false)
  }

  return (
    <div className="-mb-4 w-full px-3 pb-4 pt-1">
      <form onSubmit={handleSubmit} className="mx-auto w-full max-w-2xl">
        <div className="relative">
          <textarea
            id="chat-input"
            rows={1}
            className={`h-[108px] w-full resize-none rounded-md border py-3 pl-4 pr-24 text-black focus:outline-none ${disabled
              ? "border-gray-300 bg-gray-100 placeholder:text-gray-400 cursor-not-allowed"
              : "border-primary bg-white placeholder:text-black"
              }`}
            placeholder={placeholder}
            value={input}
            onChange={onInputChange}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault()
                handleSubmit(e)
              }
            }}
            disabled={disabled}
          />
          <div className="absolute right-3 top-1/2 flex -translate-y-1/2 gap-2">
            <button
              type="submit"
              disabled={disabled || !input.trim() || isSending}
              className={`mt-2 flex size-9 items-center justify-center rounded-full p-2 transition-colors ${disabled || isSending
                  ? "cursor-not-allowed bg-gray-400"
                  : !input.trim()
                    ? "cursor-not-allowed bg-gray-300"
                    : "cursor-pointer bg-secondary hover:bg-secondary/80"
                }`}
              title={disabled ? "Chat désactivé" : "Envoyer le message"}
            >
              {isSending ? <Spinner /> : <ArrowUp size={28} className="text-white" />}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
