import { z } from "zod"
import { TRPCError } from "@trpc/server"

import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"
import { logger } from "@buildismart/lib"

// Schémas de validation
const createChatSchema = z.object({
  projectId: z.string(),
  title: z.string().optional(),
})

const getChatSchema = z.object({
  projectId: z.string(),
})

const saveMessageSchema = z.object({
  chatId: z.string(),
  role: z.enum(["user", "assistant", "system", "data"]),
  content: z.string(),
  parts: z.any().optional(),
  annotations: z.any().optional(),
})

const getMessagesSchema = z.object({
  chatId: z.string(),
  limit: z.number().min(1).max(100).default(50),
  cursor: z.string().optional(), // Pour la pagination
})

const deleteChatSchema = z.object({
  chatId: z.string(),
})

export const chatRouter = router({
  // Créer ou récupérer un chat pour un projet
  getOrCreateChat: authenticatedProcedure
    .input(getChatSchema)
    .query(async ({ input, ctx }) => {
      const { projectId } = input
      const userId = ctx.session.user.id

      try {
        // Vérifier que le projet existe et appartient à l'utilisateur
        const project = await prisma.project.findFirst({
          where: {
            id: projectId,
            userId,
          },
        })

        if (!project) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Projet non trouvé ou accès non autorisé",
          })
        }

        // Chercher un chat existant pour ce projet
        let chat = await prisma.chat.findFirst({
          where: {
            projectId,
            userId,
          },
          include: {
            messages: {
              orderBy: { createdAt: "asc" },
              take: 50, // Limiter le nombre de messages initiaux
            },
          },
        })

        // Si aucun chat n'existe, en créer un
        if (!chat) {
          chat = await prisma.chat.create({
            data: {
              projectId,
              userId,
              title: `Chat - ${project.name}`,
            },
            include: {
              messages: true,
            },
          })
        }

        logger.log("Chat retrieved/created", {
          chatId: chat.id,
          projectId,
          messagesCount: chat.messages.length,
        })

        return {
          id: chat.id,
          projectId: chat.projectId,
          title: chat.title,
          messages: chat.messages.map((msg) => ({
            id: msg.id,
            role: msg.role,
            content: msg.content,
            parts: msg.parts,
            annotations: msg.annotations,
            createdAt: msg.createdAt,
          })),
          createdAt: chat.createdAt,
          updatedAt: chat.updatedAt,
        }
      } catch (error) {
        logger.error("Error in getOrCreateChat:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erreur lors de la récupération du chat",
        })
      }
    }),

  // Sauvegarder un message
  saveMessage: authenticatedProcedure
    .input(saveMessageSchema)
    .mutation(async ({ input, ctx }) => {
      const { chatId, role, content, parts, annotations } = input
      const userId = ctx.session.user.id

      try {
        // Vérifier que le chat appartient à l'utilisateur
        const chat = await prisma.chat.findFirst({
          where: {
            id: chatId,
            userId,
          },
        })

        if (!chat) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Chat non trouvé ou accès non autorisé",
          })
        }

        // Créer le message
        const message = await prisma.message.create({
          data: {
            chatId,
            role,
            content,
            parts: parts || null,
            annotations: annotations || null,
          },
        })

        // Mettre à jour la date de modification du chat
        await prisma.chat.update({
          where: { id: chatId },
          data: { updatedAt: new Date() },
        })

        logger.log("Message saved", {
          messageId: message.id,
          chatId,
          role,
          contentLength: content.length,
        })

        return {
          id: message.id,
          role: message.role,
          content: message.content,
          parts: message.parts,
          annotations: message.annotations,
          createdAt: message.createdAt,
        }
      } catch (error) {
        logger.error("Error saving message:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erreur lors de la sauvegarde du message",
        })
      }
    }),

  // Récupérer les messages d'un chat avec pagination
  getMessages: authenticatedProcedure
    .input(getMessagesSchema)
    .query(async ({ input, ctx }) => {
      const { chatId, limit, cursor } = input
      const userId = ctx.session.user.id

      try {
        // Vérifier que le chat appartient à l'utilisateur
        const chat = await prisma.chat.findFirst({
          where: {
            id: chatId,
            userId,
          },
        })

        if (!chat) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Chat non trouvé ou accès non autorisé",
          })
        }

        // Récupérer les messages avec pagination
        const messages = await prisma.message.findMany({
          where: { chatId },
          orderBy: { createdAt: "asc" },
          take: limit + 1, // +1 pour déterminer s'il y a une page suivante
          cursor: cursor ? { id: cursor } : undefined,
        })

        let nextCursor: string | undefined = undefined
        if (messages.length > limit) {
          const nextItem = messages.pop()
          nextCursor = nextItem!.id
        }

        return {
          messages: messages.map((msg) => ({
            id: msg.id,
            role: msg.role,
            content: msg.content,
            parts: msg.parts,
            annotations: msg.annotations,
            createdAt: msg.createdAt,
          })),
          nextCursor,
        }
      } catch (error) {
        logger.error("Error getting messages:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erreur lors de la récupération des messages",
        })
      }
    }),

  // Supprimer un chat
  deleteChat: authenticatedProcedure
    .input(deleteChatSchema)
    .mutation(async ({ input, ctx }) => {
      const { chatId } = input
      const userId = ctx.session.user.id

      try {
        // Vérifier que le chat appartient à l'utilisateur
        const chat = await prisma.chat.findFirst({
          where: {
            id: chatId,
            userId,
          },
        })

        if (!chat) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Chat non trouvé ou accès non autorisé",
          })
        }

        // Supprimer le chat (les messages seront supprimés en cascade)
        await prisma.chat.delete({
          where: { id: chatId },
        })

        logger.log("Chat deleted", { chatId, userId })

        return { success: true }
      } catch (error) {
        logger.error("Error deleting chat:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erreur lors de la suppression du chat",
        })
      }
    }),

  // Lister tous les chats d'un utilisateur
  getUserChats: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session.user.id

    try {
      const chats = await prisma.chat.findMany({
        where: { userId },
        include: {
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: { updatedAt: "desc" },
      })

      return chats.map((chat) => ({
        id: chat.id,
        projectId: chat.projectId,
        title: chat.title,
        project: chat.project,
        messageCount: chat._count.messages,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt,
      }))
    } catch (error) {
      logger.error("Error getting user chats:", error)
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erreur lors de la récupération des chats",
      })
    }
  }),
})
