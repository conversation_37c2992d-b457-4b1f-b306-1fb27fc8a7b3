import { z } from "zod"

import { chunkAndEmbedDocument } from "@/lib/ai/chunking"
import { extractContentFromBuffer } from "@/lib/ai/content-extraction"
import { updateDocumentEmbedding, updateProjectEmbedding } from "@/lib/ai/embeddings"
import { auth } from "@/lib/auth"
import { env } from "@/lib/env"
import { prisma } from "@/lib/prisma"
import { s3Client } from "@/lib/s3"
import { publicProcedure, router } from "@/lib/server/trpc"
import { ProcessingResult } from "@/types/project"
import { GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3"
import { logger } from "@buildismart/lib"
import { TRPCError } from "@trpc/server"

const FileSchema = z.object({
  name: z.string().min(1, "File name is required"),
  type: z.string().min(1, "File type is required"),
  size: z.number().positive("File size must be positive"),
  data: z.array(z.number()).min(1, "File data is required"),
})

// Processing queue with better management
class ProcessingQueue {
  private queue: Map<string, Promise<ProcessingResult>> = new Map()
  private readonly maxConcurrent = 3

  async add(documentId: string, processor: () => Promise<ProcessingResult>): Promise<void> {
    // If already processing, wait for existing process
    if (this.queue.has(documentId)) {
      await this.queue.get(documentId)
      return
    }

    // Wait if queue is full
    while (this.queue.size >= this.maxConcurrent) {
      await Promise.race(this.queue.values())
    }

    // Start processing
    const promise = processor()
    this.queue.set(documentId, promise)

    // Clean up when done
    promise.finally(() => {
      this.queue.delete(documentId)
    })
  }

  getStatus(documentId: string): "processing" | "idle" {
    return this.queue.has(documentId) ? "processing" : "idle"
  }

  async waitForCompletion(): Promise<void> {
    await Promise.all(this.queue.values())
  }
}

const processingQueue = new ProcessingQueue()

// Utility function to convert stream to buffer with memory management
async function streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
  const chunks: Buffer[] = []
  let totalSize = 0
  const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB limit

  return new Promise((resolve, reject) => {
    stream.on("data", (chunk) => {
      const buffer = Buffer.from(chunk)
      totalSize += buffer.length

      if (totalSize > MAX_FILE_SIZE) {
        reject(new Error(`File too large: ${totalSize} bytes exceeds ${MAX_FILE_SIZE} bytes limit`))
        return
      }

      chunks.push(buffer)
    })

    stream.on("error", (err) => {
      logger.error("Stream error during buffer conversion", { error: err })
      reject(err)
    })

    stream.on("end", () => {
      try {
        const result = Buffer.concat(chunks)
        logger.log("Stream to buffer conversion completed", {
          totalSize,
          chunksCount: chunks.length,
        })
        resolve(result)
      } catch (error) {
        logger.error("Error concatenating buffer chunks", { error })
        reject(error)
      }
    })
  })
}

async function ensureDocumentsFolder(projectId: string): Promise<string> {
  // Chercher un dossier Documents existant
  let documentsFolder = await prisma.folder.findFirst({
    where: {
      projectId,
      parentId: null,
      name: "Documents",
    },
  })

  // Si aucun dossier Documents n'existe, le créer
  if (!documentsFolder) {
    documentsFolder = await prisma.folder.create({
      data: {
        name: "Documents",
        projectId,
        parentId: null,
      },
    })
  }

  return documentsFolder.id
}

async function processDocumentPipeline(documentId: string): Promise<ProcessingResult> {
  const startTime = Date.now()

  try {
    logger.log("Starting document processing pipeline", { documentId })

    // Get document with file info
    const document = await prisma.projectDocument.findUnique({
      where: { id: documentId },
      include: { file: true, project: true },
    })

    if (!document) {
      throw new Error("Document not found")
    }

    // Update status to processing
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: { status: "PROCESSING_TEXT" },
    })

    // Step 1: Extract content from S3 file
    logger.log("Extracting content from file", {
      documentId,
      fileName: document.originalFileName,
      bucket: document.file.bucket,
      key: document.file.key,
    })

    if (!s3Client) {
      throw new Error("S3 client not initialized - check S3 configuration")
    }

    let s3Response
    try {
      s3Response = await s3Client.send(
        new GetObjectCommand({
          Bucket: document.file.bucket,
          Key: document.file.key,
        })
      )
    } catch (error) {
      logger.error("S3 GetObject failed", {
        documentId,
        bucket: document.file.bucket,
        key: document.file.key,
        error: error instanceof Error ? error.message : String(error),
      })
      throw new Error(`Failed to retrieve file from S3: ${error instanceof Error ? error.message : "Unknown S3 error"}`)
    }

    if (!s3Response?.Body) {
      logger.error("S3 response body is empty", {
        documentId,
        bucket: document.file.bucket,
        key: document.file.key,
        responseMetadata: s3Response?.$metadata,
      })
      throw new Error("Failed to retrieve file from S3: Empty response body")
    }

    // Convert S3 body to buffer
    let buffer: Buffer | null = null
    let extractedContent
    try {
      buffer = await streamToBuffer(s3Response.Body as NodeJS.ReadableStream)
      extractedContent = await extractContentFromBuffer(buffer, document.originalFileName, document.mimeType)
    } finally {
      // Clear buffer reference to help with garbage collection
      buffer = null
    }

    // Step 2: Update document with extracted content and summary
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: {
        status: "SUMMARIZING_FILE",
        extractedText: extractedContent.extractedText,
        fileSummary: extractedContent.summary,
      },
    })

    // Step 3: Generate and store document embedding
    logger.log("Generating document embedding", { documentId })
    await updateDocumentEmbedding(documentId)

    // Step 4: Chunk and embed document content
    logger.log("Chunking and embedding document", { documentId })
    const chunkingResult = await chunkAndEmbedDocument(documentId)

    // Step 5: Update project summary and embedding
    logger.log("Updating project embedding", { projectId: document.projectId })
    await updateProjectEmbedding(document.projectId)

    // Step 6: Mark as ready
    await prisma.projectDocument.update({
      where: { id: documentId },
      data: {
        status: "READY",
        updatedAt: new Date(),
      },
    })

    const processingTime = Date.now() - startTime
    logger.log("Document processing completed", {
      documentId,
      chunksCreated: chunkingResult.totalChunks,
      processingTime,
    })

    return {
      success: true,
      documentId,
      status: "READY",
      extractedText: extractedContent.extractedText,
      summary: extractedContent.summary,
      chunksCreated: chunkingResult.totalChunks,
      processingTime,
    }
  } catch (error) {
    const processingTime = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : "Unknown processing error"

    logger.error("Document processing failed", { documentId, error: errorMessage, processingTime })

    await prisma.projectDocument.update({
      where: { id: documentId },
      data: {
        status: "ERROR",
        processingError: errorMessage,
        updatedAt: new Date(),
      },
    })

    return {
      success: false,
      documentId,
      status: "ERROR",
      error: errorMessage,
      processingTime,
    }
  }
}

// Removed: processDocumentInBackground - replaced with processDocumentPipeline

export const uploadRouter = router({
  // Uploader un fichier
  uploadFile: publicProcedure
    .input(
      z.object({
        file: FileSchema,
        fileId: z.string(),
        parentId: z.string().nullable().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      const { file, fileId } = input
      const parentId = input.parentId

      try {
        const key = `files/${session.user.id}/${fileId}`
        const fileData = new Uint8Array(file.data)

        // Upload vers S3
        const command = new PutObjectCommand({
          Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
          Key: key,
          Body: fileData,
          ContentType: file.type,
          Metadata: {
            originalName: encodeURIComponent(file.name),
            userId: session.user.id,
            fileId,
          },
        })
        await s3Client?.send(command)

        // Enregistrer dans PostgreSQL
        const fileRecord = await prisma.file.create({
          data: {
            key,
            name: file.name,
            type: file.type,
            filetype: file.type,
            bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME!,
            endpoint: env.NEXT_PUBLIC_S3_ENDPOINT || `https://${env.NEXT_PUBLIC_S3_BUCKET_NAME}.s3.amazonaws.com`,
            size: file.size,
            userId: session.user.id,
          },
        })

        let document
        if (parentId) {
          // Vérifier si c'est un projet (dossier racine)
          const isProject = await prisma.project.findUnique({
            where: { id: parentId },
          })

          if (isProject) {
            // Si c'est un projet, utiliser le dossier Documents
            const documentsFolderId = await ensureDocumentsFolder(parentId)

            document = await prisma.projectDocument.create({
              data: {
                id: fileId,
                originalFileName: file.name,
                mimeType: file.type,
                status: "UPLOADED",
                project: { connect: { id: parentId } },
                folder: { connect: { id: documentsFolderId } },
                file: { connect: { id: fileRecord.id } },
              },
            })
          } else {
            // Si c'est un dossier normal, utiliser son projectId
            const folder = await prisma.folder.findUnique({
              where: { id: parentId },
              select: { projectId: true },
            })

            if (!folder) {
              throw new TRPCError({ code: "BAD_REQUEST", message: "Parent ID is not a valid folder or project" })
            }

            document = await prisma.projectDocument.create({
              data: {
                id: fileId,
                originalFileName: file.name,
                mimeType: file.type,
                status: "UPLOADED",
                project: { connect: { id: folder.projectId! } },
                folder: { connect: { id: parentId } },
                file: { connect: { id: fileRecord.id } },
              },
            })
          }
        }

        // Start background processing using the improved queue
        if (document) {
          await processingQueue.add(document.id, () => processDocumentPipeline(document.id))
        }

        return { key }
      } catch (error) {
        logger.error("Upload error:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Upload failed",
          cause: error instanceof Error ? error.message : String(error),
        })
      }
    }),

  // Uploader un dossier
  uploadFolder: publicProcedure
    .input(
      z.object({
        files: z
          .array(
            FileSchema.extend({
              webkitRelativePath: z.string().optional(),
            })
          )
          .optional(), // Rendre files optionnel
        parentId: z.string().nullable().optional(),
        folderName: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const session = await auth()
      if (!session?.user?.id) {
        throw new TRPCError({ code: "UNAUTHORIZED" })
      }

      const { files = [], parentId, folderName } = input

      // if (!files || files.length === 0) {
      //   throw new TRPCError({ code: 'BAD_REQUEST', message: 'No files provided' });
      //}

      try {
        const uploadedItems = []
        let projectId = parentId
        let folderId = parentId

        // Déterminer le projet et le dossier parent
        if (parentId) {
          const project = await prisma.project.findUnique({
            where: { id: parentId },
          })

          if (project) {
            // Pour un projet, utiliser le dossier Documents
            projectId = parentId
            folderId = await ensureDocumentsFolder(parentId)
          } else {
            const folder = await prisma.folder.findUnique({
              where: { id: parentId },
              select: { projectId: true },
            })

            if (!folder) {
              throw new TRPCError({ code: "BAD_REQUEST", message: "Parent ID is not a valid folder or project" })
            }

            projectId = folder.projectId
            folderId = parentId
          }
        } else {
          projectId = `project-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

          // Use the provided folder name or extract from first file, fallback to default
          let projectName = folderName || "Nouveau Projet"
          if (!projectName && files.length > 0 && files[0].webkitRelativePath) {
            const pathParts = files[0].webkitRelativePath.split("/")
            if (pathParts.length > 1) {
              projectName = pathParts[0]
            }
          }

          await prisma.project.create({
            data: {
              id: projectId,
              name: projectName,
              userId: session.user.id,
            },
          })

          // Créer le dossier Documents pour le nouveau projet
          folderId = await ensureDocumentsFolder(projectId)
        }

        // Traiter chaque fichier
        for (const file of files) {
          const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          const s3Key = `files/${session.user.id}/${fileId}`
          const fileData = new Uint8Array(file.data)

          // Upload vers S3
          const command = new PutObjectCommand({
            Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
            Key: s3Key,
            Body: fileData,
            ContentType: file.type,
          })
          await s3Client?.send(command)

          // Créer le document dans la base de données
          const projectDocument = await prisma.projectDocument.create({
            data: {
              id: fileId,
              originalFileName: file.name,
              mimeType: file.type,
              status: "UPLOADED",
              project: { connect: { id: projectId! } },
              folder: { connect: { id: folderId! } },
              file: {
                create: {
                  key: s3Key,
                  name: file.name,
                  type: file.type,
                  filetype: file.type,
                  size: file.size,
                  bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME!,
                  endpoint: env.NEXT_PUBLIC_S3_ENDPOINT || `https://${env.NEXT_PUBLIC_S3_BUCKET_NAME}.s3.amazonaws.com`,
                  userId: session.user.id,
                },
              },
            },
            include: {
              file: true,
            },
          })

          // Start background processing using the improved queue
          await processingQueue.add(projectDocument.id, () => processDocumentPipeline(projectDocument.id))

          uploadedItems.push({
            id: projectDocument.id,
            title: file.name,
            createdAt: new Date(),
            parentId: folderId,
            type: "file",
            url: s3Key,
            mimeType: file.type,
          })
        }

        return {
          success: true,
          projectId: projectId,
          folderId: folderId,
          items: uploadedItems,
        }
      } catch (error) {
        logger.error("Upload error:", error)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to upload folder",
          cause: error instanceof Error ? error.message : String(error),
        })
      }
    }),
})
