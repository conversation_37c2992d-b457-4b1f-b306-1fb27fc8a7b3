import { PrismaClient } from "@prisma/client"

const globalForPrisma = global as unknown as { prisma: PrismaClient }

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient().$extends({
    query: {
      user: {
        async $allOperations({ args, query }) {
          const result = await query(args)
          if (
            !("select" in args) ||
            (args.select &&
              typeof args.select === "object" &&
              "password" in args.select &&
              args.select.password !== true)
          ) {
            if (Array.isArray(result)) result.forEach((r) => "password" in r && delete r.password)
            else if (typeof result === "object" && result && "password" in result) delete result.password
          }
          return result
        },
      },
    },
  })

// eslint-disable-next-line no-process-env
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma
