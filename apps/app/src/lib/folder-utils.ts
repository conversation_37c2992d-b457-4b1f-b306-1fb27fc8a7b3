import { trpcClient } from "@/lib/trpc/client" // bien utiliser .client ici
import { FolderType } from "@/types/index-type"
type PayloadType = {
  files: {
    name: string
    type: string
    size: number
    data: number[]
    webkitRelativePath?: string
  }[]
  parentId: string
  folderName?: string
}
export async function getAllFolders() {
  return await trpcClient.folders.getAll.query()
}

export async function getFolderById(id: string) {
  return await trpcClient.folders.getById.query({ id })
}

export async function getFileById(id: string) {
  return await trpcClient.folders.getFileById.query({ id })
}

export async function upsertItem(item: FolderType) {
  return await trpcClient.folders.upsert.mutate({ item })
}

export async function deleteItem(id: string) {
  return await trpcClient.folders.delete.mutate({ id })
}

export async function uploadFile(file: File, parentId: string | null) {
  const fileId = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  // Lire le contenu du fichier avant l'envoi
  const arrayBuffer = await file.arrayBuffer()
  const uint8Array = new Uint8Array(arrayBuffer)

  const response = await trpcClient.upload.uploadFile.mutate({
    file: {
      name: file.name,
      type: file.type,
      size: file.size,
      data: Array.from(uint8Array), // Convertir en array simple pour la sérialisation
    },
    fileId,
    parentId,
  })

  return {
    id: fileId,
    title: file.name,
    type: "file",
    createdAt: new Date(),
    parentId,
    url: response.key,
    mimeType: file.type,
  }
}

export async function uploadFolder(files: File[], parentId: string | null) {
  try {
    // Extract folder name from the first file's webkitRelativePath
    let folderName = "Nouveau Projet"
    if (files.length > 0 && files[0].webkitRelativePath) {
      const pathParts = files[0].webkitRelativePath.split("/")
      if (pathParts.length > 1) {
        folderName = pathParts[0] // Use the root folder name
      }
    }

    // Check total size to prevent memory issues
    const totalSize = files.reduce((sum, file) => sum + file.size, 0)
    const MAX_TOTAL_SIZE = 500 * 1024 * 1024 // 500MB total limit
    if (totalSize > MAX_TOTAL_SIZE) {
      throw new Error(`Total folder size too large: ${totalSize} bytes exceeds ${MAX_TOTAL_SIZE} bytes limit`)
    }

    // Préparer les fichiers pour l'envoi avec gestion mémoire
    const preparedFiles = []
    for (const file of files) {
      try {
        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        const preparedFile = {
          name: file.name,
          type: file.type,
          size: file.size,
          data: Array.from(uint8Array),
          webkitRelativePath: file.webkitRelativePath || "", // Include path info
        }
        preparedFiles.push(preparedFile)

        // Clear references to help GC
        arrayBuffer.constructor = null as any
      } catch (error) {
        console.error(`Failed to prepare file ${file.name}:`, error)
        throw new Error(
          `Failed to prepare file ${file.name}: ${error instanceof Error ? error.message : "Unknown error"}`
        )
      }
    }

    const payload: PayloadType = {
      files: [],
      parentId: "",
      folderName, // Add folder name to payload
    }
    if (preparedFiles.length > 0) {
      payload.files = preparedFiles
    }
    if (parentId) {
      payload.parentId = parentId
    }

    const response = await trpcClient.upload.uploadFolder.mutate(payload)

    return response.items
  } catch (error) {
    console.error("Upload folder error:", error)
    throw error
  }
}
export async function generateEmbedding(text: string) {
  // Si vous voulez vraiment générer côté client (attention aux limites)
  // Sinon, passez par tRPC
  const response = await trpcClient.folders.generateEmbeddings.mutate({
    text,
  })
  return response.embedding
}

export async function updateDocumentEmbedding(documentId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    documentId,
  })
}

export async function updateProjectEmbedding(projectId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    projectId,
  })
}

export async function chunkAndEmbedDocument(documentId: string) {
  return await trpcClient.folders.generateEmbeddings.mutate({
    documentId,
    chunk: true,
  })
}
