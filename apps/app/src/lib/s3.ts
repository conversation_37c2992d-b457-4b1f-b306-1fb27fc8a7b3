import { env } from "@/lib/env"
import { S3Client } from "@aws-sdk/client-s3"
import { logger } from "@buildismart/lib"

// Validate S3 configuration
function validateS3Config() {
  if (!env.ENABLE_S3_SERVICE) {
    logger.warn("S3 service is disabled")
    return false
  }

  const requiredVars = {
    S3_REGION: env.S3_REGION,
    S3_ACCESS_KEY_ID: env.S3_ACCESS_KEY_ID,
    S3_SECRET_ACCESS_KEY: env.S3_SECRET_ACCESS_KEY,
    NEXT_PUBLIC_S3_ENDPOINT: env.NEXT_PUBLIC_S3_ENDPOINT,
    NEXT_PUBLIC_S3_BUCKET_NAME: env.NEXT_PUBLIC_S3_BUCKET_NAME,
  }

  const missingVars = Object.entries(requiredVars)
    .filter(([_, value]) => !value)
    .map(([key]) => key)

  if (missingVars.length > 0) {
    logger.error("Missing S3 configuration variables", { missingVars })
    return false
  }

  return true
}

export const s3Client = validateS3Config()
  ? new S3Client({
      region: env.S3_REGION!,
      credentials: {
        accessKeyId: env.S3_ACCESS_KEY_ID!,
        secretAccessKey: env.S3_SECRET_ACCESS_KEY!,
      },
      endpoint: env.NEXT_PUBLIC_S3_ENDPOINT?.startsWith("http")
        ? env.NEXT_PUBLIC_S3_ENDPOINT
        : `https://${env.NEXT_PUBLIC_S3_ENDPOINT}`,
      maxAttempts: 3,
    })
  : null

// Log S3 client status
if (s3Client) {
  logger.log("S3 client initialized successfully", {
    region: env.S3_REGION,
    endpoint: env.NEXT_PUBLIC_S3_ENDPOINT,
    bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME,
  })
} else {
  logger.warn("S3 client not initialized - service disabled or configuration missing")
}
